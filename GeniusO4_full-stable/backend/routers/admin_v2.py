# backend/routers/admin_v2.py

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from services.oracle_client import oracle_client
from services.redis_client import redis_client
from services.llm_router import llm_router, LLMProvider
from backend.auth.dependencies import get_current_user, require_admin
from backend.middleware.cache_middleware import CacheControl

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin/v2", tags=["admin-v2"])

# Pydantic models
class UserUpdateRequest(BaseModel):
    """Модель запроса обновления пользователя"""
    tier: Optional[str] = Field(None, description="Уровень подписки")
    expires: Optional[str] = Field(None, description="Дата окончания подписки")
    banned_until: Optional[str] = Field(None, description="Дата окончания бана")
    role: Optional[str] = Field(None, description="Роль пользователя")

class SystemConfigRequest(BaseModel):
    """Модель запроса конфигурации системы"""
    maintenance_mode: Optional[bool] = Field(None, description="Режим обслуживания")
    max_requests_per_hour: Optional[int] = Field(None, description="Лимит запросов в час")
    default_llm_provider: Optional[str] = Field(None, description="LLM провайдер по умолчанию")
    enable_new_registrations: Optional[bool] = Field(None, description="Разрешить новые регистрации")

@router.get("/dashboard")
async def get_admin_dashboard(user: Dict[str, Any] = Depends(require_admin)):
    """
    Получение данных для админ дашборда
    
    Returns:
        Статистика системы и пользователей
    """
    try:
        # Статистика пользователей
        all_users = oracle_client.query_documents('users', limit=10000)
        
        user_stats = {
            'total_users': len(all_users),
            'active_users': len([u for u in all_users if u.get('tier') != 'free']),
            'banned_users': len([u for u in all_users if u.get('banned_until')]),
            'new_users_today': 0,
            'tier_distribution': {}
        }
        
        # Распределение по тарифам
        for user in all_users:
            tier = user.get('tier', 'free')
            user_stats['tier_distribution'][tier] = user_stats['tier_distribution'].get(tier, 0) + 1
        
        # Подсчет новых пользователей за сегодня
        today = datetime.utcnow().date()
        for user in all_users:
            created_at = user.get('created_at')
            if created_at:
                try:
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).date()
                    if created_date == today:
                        user_stats['new_users_today'] += 1
                except:
                    pass
        
        # Статистика платежей
        all_payments = oracle_client.query_documents('payments', limit=10000)
        payment_stats = {
            'total_payments': len(all_payments),
            'successful_payments': len([p for p in all_payments if p.get('status') == 'completed']),
            'total_revenue': sum([p.get('amount', 0) for p in all_payments if p.get('status') == 'completed']),
            'payments_today': 0
        }
        
        # Подсчет платежей за сегодня
        for payment in all_payments:
            created_at = payment.get('created_at')
            if created_at:
                try:
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).date()
                    if created_date == today:
                        payment_stats['payments_today'] += 1
                except:
                    pass
        
        # Статистика системы
        system_stats = {
            'llm_status': llm_router.get_providers_status(),
            'cache_stats': CacheControl.get_cache_stats(),
            'redis_connected': redis_client.is_connected(),
            'uptime': "N/A"  # Можно добавить реальный uptime
        }
        
        return {
            "success": True,
            "data": {
                "user_stats": user_stats,
                "payment_stats": payment_stats,
                "system_stats": system_stats,
                "last_updated": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения админ дашборда: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users")
async def get_users(
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(50, ge=1, le=100, description="Количество пользователей на странице"),
    tier: Optional[str] = Query(None, description="Фильтр по тарифу"),
    search: Optional[str] = Query(None, description="Поиск по имени или ID"),
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Получение списка пользователей с пагинацией и фильтрацией
    
    Args:
        page: Номер страницы
        limit: Количество пользователей на странице
        tier: Фильтр по тарифу
        search: Поиск по имени или ID
        
    Returns:
        Список пользователей с метаданными
    """
    try:
        # Получаем всех пользователей
        all_users = oracle_client.query_documents('users', limit=10000)
        
        # Применяем фильтры
        filtered_users = all_users
        
        if tier:
            filtered_users = [u for u in filtered_users if u.get('tier') == tier]
        
        if search:
            search_lower = search.lower()
            filtered_users = [
                u for u in filtered_users 
                if (search_lower in u.get('first_name', '').lower() or
                    search_lower in u.get('last_name', '').lower() or
                    search_lower in u.get('username', '').lower() or
                    search_lower in u.get('_id', ''))
            ]
        
        # Пагинация
        total_users = len(filtered_users)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_users = filtered_users[start_index:end_index]
        
        # Подготавливаем данные для ответа
        users_data = []
        for user in paginated_users:
            user_data = {
                'id': user.get('_id'),
                'telegram_id': user.get('telegram_id'),
                'first_name': user.get('first_name'),
                'last_name': user.get('last_name'),
                'username': user.get('username'),
                'tier': user.get('tier', 'free'),
                'expires': user.get('expires'),
                'role': user.get('role', 'user'),
                'banned_until': user.get('banned_until'),
                'created_at': user.get('created_at'),
                'last_seen': user.get('last_seen')
            }
            users_data.append(user_data)
        
        return {
            "success": True,
            "data": {
                "users": users_data,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_users,
                    "pages": (total_users + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения списка пользователей: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users/{user_id}")
async def get_user_details(
    user_id: str,
    admin_user: Dict[str, Any] = Depends(require_admin)
):
    """
    Получение детальной информации о пользователе
    
    Args:
        user_id: ID пользователя
        
    Returns:
        Детальная информация о пользователе
    """
    try:
        user_data = oracle_client.get_document('users', user_id)
        
        if not user_data:
            raise HTTPException(status_code=404, detail="Пользователь не найден")
        
        # Получаем историю платежей
        all_payments = oracle_client.query_documents('payments', limit=1000)
        user_payments = [p for p in all_payments if p.get('user_id') == user_id]
        
        # Получаем статистику использования
        usage_stats = {
            'total_requests': 0,  # Можно добавить реальную статистику
            'last_request': None,
            'favorite_symbols': [],
            'total_payments': len(user_payments),
            'total_spent': sum([p.get('amount', 0) for p in user_payments if p.get('status') == 'completed'])
        }
        
        return {
            "success": True,
            "data": {
                "user": user_data,
                "payments": user_payments,
                "usage_stats": usage_stats
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка получения данных пользователя {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/users/{user_id}")
async def update_user(
    user_id: str,
    request: UserUpdateRequest,
    admin_user: Dict[str, Any] = Depends(require_admin)
):
    """
    Обновление данных пользователя
    
    Args:
        user_id: ID пользователя
        request: Данные для обновления
        
    Returns:
        Обновленные данные пользователя
    """
    try:
        user_data = oracle_client.get_document('users', user_id)
        
        if not user_data:
            raise HTTPException(status_code=404, detail="Пользователь не найден")
        
        # Обновляем только переданные поля
        update_data = {}
        if request.tier is not None:
            update_data['tier'] = request.tier
        if request.expires is not None:
            update_data['expires'] = request.expires
        if request.banned_until is not None:
            update_data['banned_until'] = request.banned_until
        if request.role is not None:
            update_data['role'] = request.role
        
        if update_data:
            update_data['updated_at'] = datetime.utcnow().isoformat()
            update_data['updated_by'] = admin_user.get('telegram_id')
            
            user_data.update(update_data)
            
            success = oracle_client.update_document('users', user_id, user_data)
            
            if not success:
                raise HTTPException(status_code=500, detail="Не удалось обновить пользователя")
            
            logger.info(f"Пользователь {user_id} обновлен админом {admin_user.get('telegram_id')}")
        
        return {
            "success": True,
            "data": user_data,
            "message": "Пользователь успешно обновлен"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка обновления пользователя {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/overview")
async def get_analytics_overview(
    days: int = Query(30, ge=1, le=365, description="Количество дней для анализа"),
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Получение обзорной аналитики

    Args:
        days: Количество дней для анализа

    Returns:
        Аналитические данные
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Получаем данные
        all_users = oracle_client.query_documents('users', limit=10000)
        all_payments = oracle_client.query_documents('payments', limit=10000)

        # Анализ пользователей
        user_analytics = {
            'total_users': len(all_users),
            'new_users_period': 0,
            'active_users_period': 0,
            'churn_rate': 0,
            'daily_registrations': {}
        }

        # Анализ платежей
        payment_analytics = {
            'total_revenue': 0,
            'revenue_period': 0,
            'successful_payments': 0,
            'failed_payments': 0,
            'average_payment': 0,
            'daily_revenue': {}
        }

        # Обрабатываем данные по дням
        for i in range(days):
            current_date = (start_date + timedelta(days=i)).date()
            date_str = current_date.isoformat()

            user_analytics['daily_registrations'][date_str] = 0
            payment_analytics['daily_revenue'][date_str] = 0

        # Анализируем пользователей
        for user in all_users:
            created_at = user.get('created_at')
            if created_at:
                try:
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).date()
                    if start_date.date() <= created_date <= end_date.date():
                        user_analytics['new_users_period'] += 1
                        date_str = created_date.isoformat()
                        if date_str in user_analytics['daily_registrations']:
                            user_analytics['daily_registrations'][date_str] += 1
                except:
                    pass

        # Анализируем платежи
        successful_payments = []
        for payment in all_payments:
            if payment.get('status') == 'completed':
                payment_analytics['successful_payments'] += 1
                amount = payment.get('amount', 0)
                payment_analytics['total_revenue'] += amount
                successful_payments.append(amount)

                created_at = payment.get('created_at')
                if created_at:
                    try:
                        created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).date()
                        if start_date.date() <= created_date <= end_date.date():
                            payment_analytics['revenue_period'] += amount
                            date_str = created_date.isoformat()
                            if date_str in payment_analytics['daily_revenue']:
                                payment_analytics['daily_revenue'][date_str] += amount
                    except:
                        pass
            else:
                payment_analytics['failed_payments'] += 1

        # Вычисляем средний платеж
        if successful_payments:
            payment_analytics['average_payment'] = sum(successful_payments) / len(successful_payments)

        return {
            "success": True,
            "data": {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "user_analytics": user_analytics,
                "payment_analytics": payment_analytics
            }
        }

    except Exception as e:
        logger.error(f"Ошибка получения аналитики: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/logs")
async def get_system_logs(
    level: str = Query("INFO", description="Уровень логов"),
    limit: int = Query(100, ge=1, le=1000, description="Количество записей"),
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Получение системных логов

    Args:
        level: Уровень логов (DEBUG, INFO, WARNING, ERROR)
        limit: Количество записей

    Returns:
        Системные логи
    """
    try:
        # Здесь можно реализовать чтение логов из файла или базы данных
        # Пока возвращаем заглушку

        logs = [
            {
                "timestamp": datetime.utcnow().isoformat(),
                "level": "INFO",
                "message": "System started successfully",
                "module": "main"
            },
            {
                "timestamp": (datetime.utcnow() - timedelta(minutes=5)).isoformat(),
                "level": "INFO",
                "message": "User authentication successful",
                "module": "auth"
            }
        ]

        return {
            "success": True,
            "data": {
                "logs": logs,
                "total": len(logs),
                "level": level,
                "limit": limit
            }
        }

    except Exception as e:
        logger.error(f"Ошибка получения логов: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/payments")
async def get_payments(
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(50, ge=1, le=100, description="Количество платежей на странице"),
    status: Optional[str] = Query(None, description="Фильтр по статусу"),
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Получение списка платежей
    
    Args:
        page: Номер страницы
        limit: Количество платежей на странице
        status: Фильтр по статусу
        
    Returns:
        Список платежей с метаданными
    """
    try:
        # Получаем все платежи
        all_payments = oracle_client.query_documents('payments', limit=10000)
        
        # Применяем фильтры
        filtered_payments = all_payments
        
        if status:
            filtered_payments = [p for p in filtered_payments if p.get('status') == status]
        
        # Сортируем по дате (новые первые)
        filtered_payments.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Пагинация
        total_payments = len(filtered_payments)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_payments = filtered_payments[start_index:end_index]
        
        return {
            "success": True,
            "data": {
                "payments": paginated_payments,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_payments,
                    "pages": (total_payments + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения списка платежей: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/config")
async def get_system_config(user: Dict[str, Any] = Depends(require_admin)):
    """
    Получение конфигурации системы
    
    Returns:
        Текущая конфигурация системы
    """
    try:
        config_data = oracle_client.get_document('config', 'system') or {}
        
        default_config = {
            'maintenance_mode': False,
            'max_requests_per_hour': 1000,
            'default_llm_provider': 'openai',
            'enable_new_registrations': True,
            'cache_enabled': True,
            'debug_mode': False
        }
        
        # Объединяем с дефолтными значениями
        current_config = {**default_config, **config_data}
        
        return {
            "success": True,
            "data": current_config
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения конфигурации системы: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/system/config")
async def update_system_config(
    request: SystemConfigRequest,
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Обновление конфигурации системы
    
    Args:
        request: Новые настройки конфигурации
        
    Returns:
        Обновленная конфигурация
    """
    try:
        config_data = oracle_client.get_document('config', 'system') or {}
        
        # Обновляем только переданные поля
        update_data = {}
        if request.maintenance_mode is not None:
            update_data['maintenance_mode'] = request.maintenance_mode
        if request.max_requests_per_hour is not None:
            update_data['max_requests_per_hour'] = request.max_requests_per_hour
        if request.default_llm_provider is not None:
            update_data['default_llm_provider'] = request.default_llm_provider
        if request.enable_new_registrations is not None:
            update_data['enable_new_registrations'] = request.enable_new_registrations
        
        if update_data:
            update_data['updated_at'] = datetime.utcnow().isoformat()
            update_data['updated_by'] = user.get('telegram_id')
            
            config_data.update(update_data)
            
            success = oracle_client.update_document('config', 'system', config_data)
            if not success:
                success = oracle_client.insert_document('config', 'system', config_data)
            
            if not success:
                raise HTTPException(status_code=500, detail="Не удалось обновить конфигурацию")
            
            logger.info(f"Конфигурация системы обновлена админом {user.get('telegram_id')}")
        
        return {
            "success": True,
            "data": config_data,
            "message": "Конфигурация успешно обновлена"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка обновления конфигурации системы: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/system/maintenance")
async def toggle_maintenance_mode(
    enabled: bool = Query(..., description="Включить/выключить режим обслуживания"),
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Переключение режима обслуживания
    
    Args:
        enabled: Включить или выключить режим обслуживания
        
    Returns:
        Статус режима обслуживания
    """
    try:
        config_data = oracle_client.get_document('config', 'system') or {}
        config_data['maintenance_mode'] = enabled
        config_data['maintenance_updated_at'] = datetime.utcnow().isoformat()
        config_data['maintenance_updated_by'] = user.get('telegram_id')
        
        success = oracle_client.update_document('config', 'system', config_data)
        if not success:
            success = oracle_client.insert_document('config', 'system', config_data)
        
        if not success:
            raise HTTPException(status_code=500, detail="Не удалось обновить режим обслуживания")
        
        # Также сохраняем в Redis для быстрого доступа
        redis_client.set_flag('maintenance_mode', enabled)
        
        status_text = "включен" if enabled else "выключен"
        logger.info(f"Режим обслуживания {status_text} админом {user.get('telegram_id')}")
        
        return {
            "success": True,
            "data": {
                "maintenance_mode": enabled,
                "updated_at": config_data['maintenance_updated_at']
            },
            "message": f"Режим обслуживания {status_text}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка переключения режима обслуживания: {e}")
        raise HTTPException(status_code=500, detail=str(e))
